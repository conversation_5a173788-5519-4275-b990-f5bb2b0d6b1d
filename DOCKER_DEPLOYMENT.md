# LTDWJ Docker Deployment Instructions

## Prerequisites
- Debian 12 server
- Root or sudo access
- Network access to download Docker and dependencies

## Quick Deployment

### Option 1: Automated Script (Recommended)

1. **Copy the project directory** to your Debian 12 server:
   ```bash
   # On your local machine, create a tar archive
   tar -czf LTDWJ.tar.gz LTDWJ/
   
   # Copy to server (replace with your server details)
   scp LTDWJ.tar.gz user@your-server:/home/<USER>/
   
   # On the server, extract
   cd /home/<USER>/
   tar -xzf LTDWJ.tar.gz
   cd LTDWJ/
   ```

2. **Run the deployment script**:
   ```bash
   ./deploy.sh
   ```

   The script will:
   - Install Docker if not present
   - Build the Docker image
   - Create and start the container
   - Set up automatic restart policy

### Option 2: Manual Commands

If you prefer manual control:

1. **Install Docker** (if not installed):
   ```bash
   # Update system
   sudo apt-get update
   
   # Install prerequisites
   sudo apt-get install -y ca-certificates curl gnupg lsb-release
   
   # Add Docker GPG key
   sudo mkdir -p /etc/apt/keyrings
   curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
   
   # Add Docker repository
   echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
   
   # Install Docker
   sudo apt-get update
   sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   
   # Log out and back in for group changes
   ```

2. **Build and run the container**:
   ```bash
   # Navigate to project directory
   cd /path/to/LTDWJ/
   
   # Create data directory
   mkdir -p ./data
   
   # Build the image
   docker build -t ltdwj-app .
   
   # Run the container
   docker run -d \
     --name LTDWJ-25052025 \
     --restart always \
     -p **********:8000:8000 \
     -v $(pwd)/data:/app/data \
     ltdwj-app
   ```

## Container Management

### Check Status
```bash
docker ps | grep LTDWJ-25052025
```

### View Logs
```bash
docker logs LTDWJ-25052025
docker logs -f LTDWJ-25052025  # Follow logs in real-time
```

### Stop/Start/Restart
```bash
docker stop LTDWJ-25052025
docker start LTDWJ-25052025
docker restart LTDWJ-25052025
```

### Update Application
```bash
# Stop and remove old container
docker stop LTDWJ-25052025
docker rm LTDWJ-25052025

# Rebuild image with new code
docker build -t ltdwj-app .

# Start new container
docker run -d \
  --name LTDWJ-25052025 \
  --restart always \
  -p **********:8000:8000 \
  -v $(pwd)/data:/app/data \
  ltdwj-app
```

### Remove Everything
```bash
docker stop LTDWJ-25052025
docker rm LTDWJ-25052025
docker rmi ltdwj-app
```

## Access the Application

Once deployed, access your application at:
**http://**********:8000**

## Troubleshooting

### Container won't start
```bash
# Check logs for errors
docker logs LTDWJ-25052025

# Check if port is already in use
sudo netstat -tulpn | grep :8000
```

### Database issues
```bash
# Access container shell
docker exec -it LTDWJ-25052025 /bin/bash

# Run Django commands inside container
python manage.py migrate
python manage.py createsuperuser
```

### Permission issues
```bash
# Fix data directory permissions
sudo chown -R $USER:$USER ./data
```

## Container Configuration

- **Container Name**: LTDWJ-25052025
- **Port Binding**: **********:8000 → container:8000
- **Restart Policy**: always
- **Data Volume**: ./data → /app/data (for SQLite database)
- **Base Image**: python:3.12-slim

## Security Notes

- The container runs as a non-root user for security
- Only port 8000 is exposed
- Database files are stored in a persistent volume
- Application is bound to specific IP (**********) for security
