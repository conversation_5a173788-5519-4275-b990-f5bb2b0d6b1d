{% extends 'JW/base.html' %}

{% block title %}Manage Report Header{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Manage Report Header</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% for field_name, field_data in headers.items %}
                            <div class="row mb-3 align-items-center">
                                <div class="col-md-3">
                                    <label for="id_{{ field_name }}" class="form-label">{{ field_name|title }}</label>
                                </div>
                                <div class="col-md-7">
                                    <input type="text"
                                           name="{{ field_name }}"
                                           id="id_{{ field_name }}"
                                           value="{{ field_data.value }}"
                                           class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <div class="form-check">
                                        <input type="checkbox"
                                               name="{{ field_name }}_enabled"
                                               id="id_{{ field_name }}_enabled"
                                               class="form-check-input"
                                               {% if field_data.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="id_{{ field_name }}_enabled">
                                            Show
                                        </label>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}

                        <!-- Tax/NI Percentage Section -->
                        <hr class="my-4">
                        <h5 class="mb-3">Tax & National Insurance Settings</h5>
                        <div class="row mb-3 align-items-center">
                            <div class="col-md-3">
                                <label for="{{ settings_form.tax_ni_percentage.id_for_label }}" class="form-label">{{ settings_form.tax_ni_percentage.label }}</label>
                            </div>
                            <div class="col-md-4">
                                {{ settings_form.tax_ni_percentage }}
                                {% if settings_form.tax_ni_percentage.errors %}
                                    <div class="text-danger small">
                                        {% for error in settings_form.tax_ni_percentage.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-5">
                                <small class="text-muted">
                                    This combined percentage gives a good estimate of Income Tax and NI deductions for earnings up to £50,000 per year in 2025
                                </small>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
