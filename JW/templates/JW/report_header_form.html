{% extends 'JW/base.html' %}

{% block title %}Manage Report Header{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Manage Report Header</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% for field_name, field_data in headers.items %}
                            <div class="row mb-3 align-items-center">
                                <div class="col-md-3">
                                    <label for="id_{{ field_name }}" class="form-label">{{ field_name|title }}</label>
                                </div>
                                <div class="col-md-7">
                                    <input type="text" 
                                           name="{{ field_name }}" 
                                           id="id_{{ field_name }}" 
                                           value="{{ field_data.value }}" 
                                           class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               name="{{ field_name }}_enabled" 
                                               id="id_{{ field_name }}_enabled" 
                                               class="form-check-input"
                                               {% if field_data.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="id_{{ field_name }}_enabled">
                                            Show
                                        </label>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
