{% extends 'JW/base.html' %}

{% block title %}Lessons for {{ student_name }}{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>Lessons for {{ student_name }}</h2>
            <div class="text-muted mt-2">
                <strong>Total Lessons:</strong> {{ lessons|length }} |
                <strong>Total Hours:</strong> {{ total_hours|floatformat:1 }} |
                <strong>Total Amount:</strong> £{{ total_amount|floatformat:2 }}
            </div>
        </div>
        <div>
            <a href="{% url 'lesson_list' %}" class="btn btn-secondary me-2">Back to All Lessons</a>
            <a href="{% url 'lesson_create' %}?student_name={{ student_name|urlencode }}" class="btn btn-primary">Add New Lesson</a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th style="width: 12%">Date</th>
                    <th style="width: 12%">Day</th>
                    <th style="width: 20%">Student</th>
                    <th style="width: 8%">Hours</th>
                    <th style="width: 12%">Price/Hour</th>
                    <th style="width: 12%">Amount</th>
                    <th style="width: 24%">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for lesson in lessons %}
                <tr>
                    <td>{{ lesson.date|date:"M d, Y" }}</td>
                    <td>{{ lesson.day_of_week }}</td>
                    <td>{{ lesson.student_name }}</td>
                    <td>{{ lesson.lesson_hours }}</td>
                    <td>£{{ lesson.price_per_hour|floatformat:2 }}</td>
                    <td>£{{ lesson.calculated_amount|floatformat:2 }}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'lesson_update' lesson.pk %}" class="btn btn-sm btn-warning">Edit</a>
                            <a href="{% url 'lesson_delete' lesson.pk %}" class="btn btn-sm btn-danger">Delete</a>
                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#notesModal{{ lesson.pk }}">
                                Notes
                            </button>
                        </div>
                    </td>
                </tr>
                <!-- Notes Modal for each lesson -->
                <div class="modal fade" id="notesModal{{ lesson.pk }}" tabindex="-1" aria-labelledby="notesModalLabel{{ lesson.pk }}" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="notesModalLabel{{ lesson.pk }}">
                                    Notes for {{ lesson.student_name }} - {{ lesson.date|date:"M d, Y" }}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                {% if lesson.notes %}
                                    <p class="mb-0">{{ lesson.notes|linebreaks }}</p>
                                {% else %}
                                    <p class="text-muted mb-0">No notes available.</p>
                                {% endif %}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <tr>
                    <td colspan="7" class="text-center">No lessons found for {{ student_name }}.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
