{% extends 'JW/base.html' %}

{% block title %}Delete Mileage Record{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0">Delete Mileage Record</h2>
            </div>
            <div class="card-body">
                <p>Are you sure you want to delete this mileage record?</p>
                <ul class="list-unstyled">
                    <li><strong>Date:</strong> {{ mileage.date|date:"M d, Y" }}</li>
                    <li><strong>Type:</strong> {{ mileage.mileage_type }}</li>
                    <li><strong>Description:</strong> {{ mileage.description }}</li>
                    <li><strong>Miles:</strong> {{ mileage.miles }}</li>
                </ul>
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                    <a href="{% url 'mileage_list' %}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
