{% extends 'JW/base.html' %}

{% block title %}Monthly Report - {{ month_name }} {{ year }}{% endblock %}

{% block content %}
<div class="container">
    {% if report_header %}
        <div class="text-center mb-4">
            {% if report_header.name %}<h3 class="mb-2">{{ report_header.name }}</h3>{% endif %}
            {% if report_header.address %}<p class="mb-2">{{ report_header.address }}</p>{% endif %}
            {% if report_header.phone %}<p class="mb-2">{{ report_header.phone }}</p>{% endif %}
            {% if report_header.email %}<p class="mb-2">{{ report_header.email }}</p>{% endif %}
            {% if report_header.website %}<p class="mb-2">{{ report_header.website }}</p>{% endif %}
            {% if report_header.business_number %}<p class="mb-2">Business Number: {{ report_header.business_number }}</p>{% endif %}
        </div>
    {% endif %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Financial Report - {{ month_name }} {{ year }}</h2>
        <div>
            <a href="{% url 'monthly_report_pdf' %}?month={{ month }}&year={{ year }}" class="btn btn-success">
                <i class="fas fa-download"></i> Download PDF
            </a>
            <a href="{% url 'report_list' %}" class="btn btn-secondary ms-2">
                Back to Reports
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">Income Summary</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped w-100">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Student</th>
                            <th>Hours</th>
                            <th>Price/Hour</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lesson in lessons %}
                        <tr>
                            <td>{{ lesson.date|date:"d/m/Y" }}</td>
                            <td>{{ lesson.student_name }}</td>
                            <td>{{ lesson.lesson_hours }}</td>
                            <td>£{{ lesson.price_per_hour|floatformat:2 }}</td>
                            <td class="text-end">£{{ lesson.amount|default:0|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                        <tr class="table-success">
                            <td colspan="4"><strong>Total Income</strong></td>
                            <td class="text-end"><strong>£{{ total_income|floatformat:2 }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h4 class="mb-0">Business Expenses</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped w-100">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in business_expenses %}
                        <tr>
                            <td>{{ expense.date|date:"d/m/Y" }}</td>
                            <td>{{ expense.expense_type }}</td>
                            <td>{{ expense.description|default:expense.notes|default:'' }}</td>
                            <td class="text-end">£{{ expense.cost|default:0|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                        <tr class="table-danger">
                            <td colspan="3"><strong>Total Business Expenses</strong></td>
                            <td class="text-end"><strong>£{{ total_business_expenses|floatformat:2 }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0">Fuel Expenses</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped w-100">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Day</th>
                            <th>Notes</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in fuel_expenses %}
                        <tr>
                            <td>{{ expense.date|date:"d/m/Y" }}</td>
                            <td>{{ expense.day_of_week }}</td>
                            <td>{{ expense.notes|default:'' }}</td>
                            <td class="text-end">£{{ expense.cost|default:0|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                        <tr class="table-info">
                            <td colspan="3"><strong>Total Fuel Expenses</strong></td>
                            <td class="text-end"><strong>£{{ total_fuel_expenses|floatformat:2 }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h4 class="mb-0">Mileage Summary</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-striped w-100">
                        <tbody>
                            <tr>
                                <th>Total Miles:</th>
                                <td class="text-end">{{ total_miles|floatformat:1 }}</td>
                            </tr>
                            <tr>
                                <th>Business Miles:</th>
                                <td class="text-end">{{ total_business_miles|floatformat:1 }} ({{ business_miles_percentage|floatformat:1 }}%)</td>
                            </tr>
                            <tr>
                                <th>Personal Miles:</th>
                                <td class="text-end">{{ total_personal_miles|floatformat:1 }} ({{ personal_miles_percentage|floatformat:1 }}%)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">Financial Summary</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <tbody>
                    <tr class="table-success">
                        <th>Total Income</th>
                        <td class="text-end">£{{ total_income|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-danger">
                        <th>Total Business Expenses</th>
                        <td class="text-end">£{{ total_business_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-info">
                        <th>Total Fuel Expenses</th>
                        <td class="text-end">£{{ total_fuel_expenses|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-primary">
                        <th><strong>Net Income</strong></th>
                        <td class="text-end"><strong>£{{ net_income|floatformat:2 }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
