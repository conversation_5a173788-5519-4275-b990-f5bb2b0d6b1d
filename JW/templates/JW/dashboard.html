{% extends 'JW/base.html' %}

{% block title %}Dashboard{% endblock %}

{% block content %}
<style>
    @media (min-width: 1200px) {
        .dashboard-btn {
            aspect-ratio: 1;
            padding: 2rem !important;
        }
        .dashboard-btn i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .dashboard-btn .btn-text {
            font-size: 1.1rem;
        }
    }
    
    .dashboard-btn {
        transition: all 0.3s ease;
        border: 2px solid #000000;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        color: #000000 !important;
        font-weight: bold;
    }
    
    .dashboard-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
        background-color: #ffffff !important;
    }
    
    .section-header {
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #2c3e50;
        border-bottom: 2px solid #2c3e50;
        padding-bottom: 0.5rem;
    }
    
    .student-btn { background-color: #3498db; }
    .lesson-btn { background-color: #2ecc71; }
    .expense-btn { background-color: #e74c3c; }
    .mileage-btn { background-color: #9b59b6; }
    .fuel-btn { background-color: #f39c12; }
</style>

<div class="container mt-4">
    <h2 class="section-header">Quickly Add Records For</h2>
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-3">
        <div class="col">
            <a href="{% url 'student_create' %}?return_url={% url 'dashboard' %}" 
               class="btn btn-primary dashboard-btn student-btn w-100 h-100 d-flex align-items-center justify-content-center py-3">
                <div class="text-center">
                    <i class="fas fa-user-graduate mb-2"></i>
                    <div class="btn-text">Student</div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'lesson_create' %}?return_url={% url 'dashboard' %}" 
               class="btn btn-primary dashboard-btn lesson-btn w-100 h-100 d-flex align-items-center justify-content-center py-3">
                <div class="text-center">
                    <i class="fas fa-chalkboard-teacher mb-2"></i>
                    <div class="btn-text">Lesson</div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'business_expense_create' %}?return_url={% url 'dashboard' %}" 
               class="btn btn-primary dashboard-btn expense-btn w-100 h-100 d-flex align-items-center justify-content-center py-3">
                <div class="text-center">
                    <i class="fas fa-file-invoice-dollar mb-2"></i>
                    <div class="btn-text">Expense</div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'mileage_create' %}?return_url={% url 'dashboard' %}" 
               class="btn btn-primary dashboard-btn mileage-btn w-100 h-100 d-flex align-items-center justify-content-center py-3">
                <div class="text-center">
                    <i class="fas fa-route mb-2"></i>
                    <div class="btn-text">Mileage</div>
                </div>
            </a>
        </div>
        <div class="col">
            <a href="{% url 'fuel_expense_create' %}?return_url={% url 'dashboard' %}" 
               class="btn btn-primary dashboard-btn fuel-btn w-100 h-100 d-flex align-items-center justify-content-center py-3">
                <div class="text-center">
                    <i class="fas fa-gas-pump mb-2"></i>
                    <div class="btn-text">Fuel</div>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}