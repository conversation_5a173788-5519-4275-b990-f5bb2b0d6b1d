{% extends 'JW/base.html' %}

{% block title %}Personal Mileage{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Personal Mileage</h2>
    <a href="{% url 'personal_mileage_create' %}" class="btn btn-primary">Add New Mileage</a>
</div>

<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Date</th>
                <th>Day</th>
                <th>Mileage</th>
                <th>Notes</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for mileage in mileages %}
            <tr>
                <td>{{ mileage.date }}</td>
                <td>{{ mileage.day_of_week }}</td>
                <td>{{ mileage.mileage }}</td>
                <td>{{ mileage.notes|truncatechars:30 }}</td>
                <td>
                    <a href="{% url 'personal_mileage_update' mileage.pk %}" class="btn btn-sm btn-warning">Edit</a>
                    <a href="{% url 'personal_mileage_delete' mileage.pk %}" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="text-center">No personal mileage records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
