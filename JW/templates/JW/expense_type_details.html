{% extends 'JW/base.html' %}

{% block title %}{{ expense_type }} Expenses{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>{{ expense_type }} Expenses</h2>
            <div class="text-muted mt-2">
                <strong>Total Expenses:</strong> {{ expenses|length }} |
                <strong>Total Cost:</strong> £{{ total_cost|floatformat:2 }}
            </div>
        </div>
        <div>
            <a href="{% url 'business_expense_list' %}" class="btn btn-secondary me-2">Back to All Expenses</a>
            <a href="{% url 'business_expense_create' %}?expense_type={{ expense_type|urlencode }}" class="btn btn-primary">Add New Expense</a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th style="width: 12%">Date</th>
                    <th style="width: 12%">Day</th>
                    <th style="width: 15%">Type</th>
                    <th style="width: 15%">Category</th>
                    <th style="width: 12%">Cost</th>
                    <th style="width: 34%">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for expense in expenses %}
                <tr>
                    <td>{{ expense.date|date:"M d, Y" }}</td>
                    <td>{{ expense.day_of_week }}</td>
                    <td>
                        <a href="{% url 'expense_type_details' expense_type=expense.expense_type %}" class="text-decoration-none">
                            {{ expense.expense_type }}
                        </a>
                    </td>
                    <td>{{ expense.category }}</td>
                    <td>£{{ expense.cost|floatformat:2 }}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'business_expense_update' expense.pk %}" class="btn btn-sm btn-warning">Edit</a>
                            <a href="{% url 'business_expense_delete' expense.pk %}" class="btn btn-sm btn-danger">Delete</a>
                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#descriptionModal{{ expense.pk }}">
                                Description
                            </button>
                        </div>
                    </td>
                </tr>
                <!-- Description Modal for each expense -->
                <div class="modal fade" id="descriptionModal{{ expense.pk }}" tabindex="-1" aria-labelledby="descriptionModalLabel{{ expense.pk }}" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="descriptionModalLabel{{ expense.pk }}">
                                    Description for {{ expense.expense_type }} - {{ expense.date|date:"M d, Y" }}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                {% if expense.description %}
                                    <p class="mb-0">{{ expense.description|linebreaks }}</p>
                                {% else %}
                                    <p class="text-muted mb-0">No description available.</p>
                                {% endif %}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center">No expenses found for {{ expense_type }}.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
