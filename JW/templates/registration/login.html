{% extends "JW/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Login{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card mt-4">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Login</h2>
                
                {% if form.errors %}
                <div class="alert alert-danger">
                    Your username and password didn't match. Please try again.
                </div>
                {% endif %}
                
                {% if next %}
                    {% if user.is_authenticated %}
                    <div class="alert alert-info">
                        Your account doesn't have access to this page. To proceed,
                        please login with an account that has access.
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        Please login to see this page.
                    </div>
                    {% endif %}
                {% endif %}
                
                <form method="post" action="{% url 'login' %}">
                    {% csrf_token %}
                    {{ form|crispy }}
                    <input type="hidden" name="next" value="{{ next }}">
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
