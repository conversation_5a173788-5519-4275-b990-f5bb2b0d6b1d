-- Drop existing mileage table if it exists
DROP TABLE IF EXISTS app_jw_mileage;

-- Create the new mileage table
CREATE TABLE IF NOT EXISTS app_jw_mileage (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    day VARCHAR(10),
    mileage_type VARCHAR(10) NOT NULL,
    miles DECIMAL(5,1) NOT NULL,
    notes TEXT
);

-- <PERSON>reate indexes
CREATE INDEX idx_app_jw_mileage_date ON app_jw_mileage(date);

-- Import business mileage data
INSERT INTO app_jw_mileage (date, day, mileage_type, miles, notes)
SELECT 
    date,
    day_of_week as day,
    'Business' as mileage_type,
    mileage as miles,
    notes
FROM "APP_JW_businessmileage";

-- Import personal mileage data
INSERT INTO app_jw_mileage (date, day, mileage_type, miles, notes)
SELECT 
    date,
    day_of_week as day,
    'Personal' as mileage_type,
    mileage as miles,
    notes
FROM "APP_JW_personalmileage";
