-- Create the block booking table
CREATE TABLE IF NOT EXISTS "APP_JW_block_booking" (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL REFERENCES "APP_JW_student"(id),
    date_created DATE NOT NULL,
    amount_paid DECIMAL(6, 2) NOT NULL,
    total_lessons DECIMAL(4, 1) NOT NULL,
    lessons_used DECIMAL(4, 1) NOT NULL DEFAULT 0,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT
);

-- Create indexes
CREATE INDEX idx_app_jw_block_booking_student_id ON "APP_JW_block_booking"(student_id);
CREATE INDEX idx_app_jw_block_booking_active ON "APP_JW_block_booking"(active);

-- Create the block booking usage table
CREATE TABLE IF NOT EXISTS "APP_JW_block_booking_usage" (
    id SERIAL PRIMARY KEY,
    block_booking_id INTEGER NOT NULL REFERENCES "APP_JW_block_booking"(id),
    lesson_id INTEGER NOT NULL REFERENCES "APP_JW_lesson"(id),
    lessons_used DECIMAL(4, 1) NOT NULL,
    date_used DATE NOT NULL
);

-- Create indexes
CREATE INDEX idx_app_jw_block_booking_usage_block_booking_id ON "APP_JW_block_booking_usage"(block_booking_id);
CREATE INDEX idx_app_jw_block_booking_usage_lesson_id ON "APP_JW_block_booking_usage"(lesson_id);
