-- Lesson table indexes
CREATE INDEX IF NOT EXISTS idx_app_jw_lesson_date ON "APP_JW_lesson"(date);
CREATE INDEX IF NOT EXISTS idx_app_jw_lesson_student_name_date ON "APP_JW_lesson"(student_name, date);

-- Business expense indexes
CREATE INDEX IF NOT EXISTS idx_app_jw_businessexpense_date ON "APP_JW_businessexpense"(date);
CREATE INDEX IF NOT EXISTS idx_app_jw_businessexpense_type_date ON "APP_JW_businessexpense"(expense_type, date);

-- Fuel expense indexes
CREATE INDEX IF NOT EXISTS idx_app_jw_fuelexpense_date ON "APP_JW_fuelexpense"(date);

-- Mileage table indexes
CREATE INDEX IF NOT EXISTS idx_app_jw_mileage_type_date ON "app_jw_mileage"(mileage_type, date);