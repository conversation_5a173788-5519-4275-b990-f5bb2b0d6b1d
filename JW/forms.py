from django import forms
from .models import (
    AppJw<PERSON>esson, AppJwStudent, AppJwBusinessexpense,
    AppJwBusinessmileage, AppJwPersonalmileage,
    AppJwMileage, AppJwFuelexpense, AppJwBlockBooking,
    AppJwBlockBookingUsage
)

class LessonForm(forms.ModelForm):
    class Meta:
        model = AppJwLesson
        fields = [
            'date',
            'day_of_week',
            'student_name',
            'lesson_hours',
            'price_per_hour',
            'notes'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'student_name': forms.TextInput(attrs={
                'class': 'form-control',
                'list': 'student-list',
                'required': True
            }),
            'lesson_hours': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'price_per_hour': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_student_name(self):
        student_name = self.cleaned_data.get('student_name')
        if not student_name:
            raise forms.ValidationError("Student name is required")

        # Strict validation - must be an active student
        exists = AppJwStudent.objects.filter(
            student_name=student_name,
            active='Yes'
        ).exists()
        if not exists:
            raise forms.ValidationError(
                "Please select an active student from the list. "
                "If this is a new student, please add them first."
            )
        return student_name

    def clean(self):
        cleaned_data = super().clean()
        lesson_hours = cleaned_data.get('lesson_hours')
        price_per_hour = cleaned_data.get('price_per_hour')
        action = self.data.get('action')  # Get the form action

        errors = {}

        # Only validate non-zero values if not a test result action
        if action not in ['passed', 'failed']:
            if lesson_hours is not None:
                if lesson_hours < 0:
                    errors['lesson_hours'] = "Lesson hours cannot be negative"
                elif lesson_hours == 0:
                    errors['lesson_hours'] = "Lesson hours must be greater than 0"

            if price_per_hour is not None:
                if price_per_hour < 0:
                    errors['price_per_hour'] = "Price per hour cannot be negative"
                elif price_per_hour == 0:
                    errors['price_per_hour'] = "Price per hour must be greater than 0"

        if errors:
            raise forms.ValidationError(errors)

        return cleaned_data

class StudentForm(forms.ModelForm):
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
        ('Other', 'Other'),
    ]

    gender = forms.ChoiceField(
        choices=GENDER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    class Meta:
        model = AppJwStudent
        fields = [
            'student_name',
            'gender',
            'age',
            'email_address',
            'mobile_number',
            'area',
            'address_1st_line',
            'address_2nd_line',
            'post_code',
            'active',
            'notes'
        ]
        widgets = {
            'student_name': forms.TextInput(attrs={'class': 'form-control'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'age': forms.NumberInput(attrs={'class': 'form-control'}),
            'email_address': forms.EmailInput(attrs={'class': 'form-control'}),
            'mobile_number': forms.TextInput(attrs={'class': 'form-control'}),
            'area': forms.TextInput(attrs={'class': 'form-control', 'list': 'area-list'}),
            'address_1st_line': forms.TextInput(attrs={'class': 'form-control'}),
            'address_2nd_line': forms.TextInput(attrs={'class': 'form-control'}),
            'post_code': forms.TextInput(attrs={'class': 'form-control'}),
            'active': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

class BusinessExpenseForm(forms.ModelForm):
    class Meta:
        model = AppJwBusinessexpense
        fields = ['date', 'day_of_week', 'expense_type', 'cost', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'expense_type': forms.TextInput(attrs={
                'class': 'form-control',
                'list': 'expense-type-list',
                'required': True
            }),
            'cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_expense_type(self):
        expense_type = self.cleaned_data.get('expense_type')
        if not expense_type:
            raise forms.ValidationError("Expense type is required")
        if len(expense_type) > 20:  # Based on model field length
            raise forms.ValidationError("Expense type cannot exceed 20 characters")
        return expense_type.strip()

    def clean_cost(self):
        cost = self.cleaned_data.get('cost')
        if cost is None:
            raise forms.ValidationError("Cost is required")
        if cost < 0:
            raise forms.ValidationError("Cost cannot be negative")
        if cost == 0:
            raise forms.ValidationError("Cost must be greater than 0")
        return cost

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day_of_week
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day_of_week'] = days[date.weekday()]

        return cleaned_data

class BusinessMileageForm(forms.ModelForm):
    class Meta:
        model = AppJwBusinessmileage
        fields = ['date', 'day_of_week', 'mileage', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'mileage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_mileage(self):
        mileage = self.cleaned_data.get('mileage')
        if mileage is None:
            raise forms.ValidationError("Mileage is required")
        if mileage < 0:
            raise forms.ValidationError("Mileage cannot be negative")
        if mileage == 0:
            raise forms.ValidationError("Mileage must be greater than 0")
        return mileage

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day_of_week based on date
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day_of_week'] = days[date.weekday()]
        else:
            raise forms.ValidationError({'date': 'Date is required'})

        return cleaned_data

class PersonalMileageForm(forms.ModelForm):
    class Meta:
        model = AppJwPersonalmileage
        fields = ['date', 'day_of_week', 'mileage', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day_of_week': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'mileage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_mileage(self):
        mileage = self.cleaned_data.get('mileage')
        if mileage is None:
            raise forms.ValidationError("Mileage is required")
        if mileage < 0:
            raise forms.ValidationError("Mileage cannot be negative")
        if mileage == 0:
            raise forms.ValidationError("Mileage must be greater than 0")
        return mileage

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day_of_week based on date
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day_of_week'] = days[date.weekday()]
        else:
            raise forms.ValidationError({'date': 'Date is required'})

        return cleaned_data

class MileageForm(forms.ModelForm):
    class Meta:
        model = AppJwMileage
        fields = ['date', 'day', 'miles', 'mileage_type', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'day': forms.TextInput(attrs={'class': 'form-control', 'readonly': True}),
            'mileage_type': forms.Select(attrs={'class': 'form-control'}),
            'miles': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_miles(self):
        miles = self.cleaned_data.get('miles')
        if miles is None:
            raise forms.ValidationError("Mileage is required")
        if miles < 0:
            raise forms.ValidationError("Mileage cannot be negative")
        if miles == 0:
            raise forms.ValidationError("Mileage must be greater than 0")
        return miles

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')

        if date:
            # Auto-populate day based on date
            days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            cleaned_data['day'] = days[date.weekday()]
        else:
            raise forms.ValidationError({'date': 'Date is required'})

        return cleaned_data

class FuelExpenseForm(forms.ModelForm):
    class Meta:
        model = AppJwFuelexpense
        fields = ['date', 'cost', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_cost(self):
        cost = self.cleaned_data.get('cost')
        if cost is None:
            raise forms.ValidationError("Cost is required")
        if cost < 0:
            raise forms.ValidationError("Cost cannot be negative")
        if cost == 0:
            raise forms.ValidationError("Cost must be greater than 0")
        return cost

    def clean_date(self):
        date = self.cleaned_data.get('date')
        if not date:
            raise forms.ValidationError("Date is required")
        return date

class ReportHeaderForm(forms.Form):
    name = forms.CharField(max_length=255, required=False)
    name_enabled = forms.BooleanField(required=False)

    address = forms.CharField(max_length=255, required=False)
    address_enabled = forms.BooleanField(required=False)

    phone = forms.CharField(max_length=255, required=False)
    phone_enabled = forms.BooleanField(required=False)

    reference = forms.CharField(max_length=255, required=False)
    reference_enabled = forms.BooleanField(required=False)

    other = forms.CharField(max_length=255, required=False)
    other_enabled = forms.BooleanField(required=False)

class BlockBookingForm(forms.ModelForm):
    class Meta:
        model = AppJwBlockBooking
        fields = ['date_created', 'amount_paid', 'total_lessons', 'notes']
        widgets = {
            'date_created': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'amount_paid': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'total_lessons': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_amount_paid(self):
        amount_paid = self.cleaned_data.get('amount_paid')
        if amount_paid is None:
            raise forms.ValidationError("Amount paid is required")
        if amount_paid <= 0:
            raise forms.ValidationError("Amount paid must be greater than 0")
        return amount_paid

    def clean_total_lessons(self):
        total_lessons = self.cleaned_data.get('total_lessons')
        if total_lessons is None:
            raise forms.ValidationError("Total lessons is required")
        if total_lessons <= 0:
            raise forms.ValidationError("Total lessons must be greater than 0")
        return total_lessons

    def clean_date_created(self):
        date_created = self.cleaned_data.get('date_created')
        if not date_created:
            raise forms.ValidationError("Date is required")
        return date_created
