from django.contrib import messages
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from django.http import JsonResponse

class FormValidationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        if isinstance(exception, (IntegrityError, ValidationError)):
            is_ajax = request.headers.get('x-requested-with') == 'XMLHttpRequest'
            
            if is_ajax:
                return JsonResponse({'error': 'Please check that you have entered data in the correct format'}, status=400)
            
            messages.error(
                request,
                'Please check that you have entered data in the correct format'
            )
            return None  # Let the view handle the response
