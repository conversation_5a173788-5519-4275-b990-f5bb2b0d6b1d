from django.core.management.base import BaseCommand
import calendar
from JW.models import AppJwFuelexpense

class Command(BaseCommand):
    help = 'Updates the day_of_week field for all fuel expenses based on their dates'

    def handle(self, *args, **options):
        expenses = AppJwFuelexpense.objects.all()
        updated_count = 0

        for expense in expenses:
            if expense.date:
                correct_day = calendar.day_name[expense.date.weekday()]
                if expense.day_of_week != correct_day:
                    expense.day_of_week = correct_day
                    expense.save()
                    updated_count += 1

        self.stdout.write(self.style.SUCCESS(
            f'Successfully updated {updated_count} fuel expense records'
        ))
