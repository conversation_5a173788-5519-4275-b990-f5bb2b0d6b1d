from django.core.management.base import BaseCommand
from django.db import connection
import os

class Command(BaseCommand):
    help = 'Creates the student table and populates it with unique students from lessons'

    def handle(self, *args, **options):
        # Read the SQL file
        sql_file_path = os.path.join('JW', 'sql', 'create_student_table.sql')
        with open(sql_file_path, 'r') as sql_file:
            sql = sql_file.read()

        # Execute the SQL
        with connection.cursor() as cursor:
            cursor.execute(sql)

        self.stdout.write(self.style.SUCCESS('Successfully created and populated student table'))