# Generated by Django 4.2 on 2025-02-20 13:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('JW', '0002_copy_notes_to_description'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppJwBusinessexpense',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('day_of_week', models.CharField(max_length=10)),
                ('date', models.DateField()),
                ('expense_type', models.Char<PERSON>ield(max_length=20)),
                ('cost', models.DecimalField(decimal_places=2, max_digits=6)),
                ('notes', models.TextField(blank=True, null=True)),
                ('category', models.CharField(blank=True, max_length=20, null=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'APP_JW_businessexpense',
                'ordering': ['-date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwBusinessmileage',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('day_of_week', models.CharField(max_length=10)),
                ('date', models.DateField()),
                ('mileage', models.DecimalField(decimal_places=1, max_digits=5)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'APP_JW_businessmileage',
                'ordering': ['-date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwFuelexpense',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('day_of_week', models.CharField(max_length=10)),
                ('date', models.DateField()),
                ('cost', models.DecimalField(decimal_places=2, max_digits=6)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'APP_JW_fuelexpense',
                'ordering': ['-date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwLesson',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('day_of_week', models.CharField(max_length=10)),
                ('date', models.DateField()),
                ('lesson_hours', models.DecimalField(decimal_places=1, max_digits=4)),
                ('student_name', models.CharField(max_length=30)),
                ('notes', models.TextField(blank=True, null=True)),
                ('price_per_hour', models.DecimalField(decimal_places=2, max_digits=5)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
            ],
            options={
                'db_table': 'APP_JW_lesson',
                'ordering': ['-date'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwMileage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('day', models.CharField(blank=True, max_length=10)),
                ('mileage_type', models.CharField(choices=[('Business', 'Business'), ('Personal', 'Personal')], max_length=10)),
                ('miles', models.DecimalField(decimal_places=1, max_digits=5)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'app_jw_mileage',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppJwPersonalmileage',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('day_of_week', models.CharField(max_length=10)),
                ('date', models.DateField()),
                ('mileage', models.DecimalField(decimal_places=1, max_digits=5)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'APP_JW_personalmileage',
                'ordering': ['-date'],
                'managed': False,
            },
        ),
    ]
