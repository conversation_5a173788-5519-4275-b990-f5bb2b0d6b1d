from django.db import migrations
from django.db import connection

def copy_notes_to_description(apps, schema_editor):
    with connection.cursor() as cursor:
        cursor.execute("""
            UPDATE "APP_JW_businessexpense"
            SET description = notes
            WHERE description IS NULL AND notes IS NOT NULL
        """)

class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.RunPython(copy_notes_to_description),
    ]
